<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推荐食谱页面预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background-color: #f5f5f5;
            max-width: 375px;
            margin: 0 auto;
            height: 100vh;
            overflow-y: auto;
            padding-bottom: 20px;
        }
        
        /* 自定义导航栏 */
        .custom-navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 22px 16px 10px 16px;
            background: #fff;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 1px 4px rgba(0,0,0,0.05);
        }
        
        .nav-back {
            width: 30px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #f5f5f5;
            border-radius: 15px;
            cursor: pointer;
        }
        
        .nav-back-icon {
            font-size: 16px;
            color: #333;
        }
        
        .nav-title {
            flex: 1;
            text-align: center;
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin: 0 10px;
        }
        
        .nav-help {
            width: 30px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #4a90e2;
            border-radius: 15px;
            cursor: pointer;
        }
        
        .help-icon {
            font-size: 14px;
            color: #fff;
            font-weight: 600;
        }
        
        /* 食材标签区域 */
        .ingredients-section {
            background: #fff;
            padding: 16px;
            margin: 10px 8px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.03);
        }
        
        .ingredients-header {
            margin-bottom: 10px;
        }
        
        .ingredients-label {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }
        
        .ingredients-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .ingredient-tag {
            background: #e8f5e8;
            border-radius: 10px;
            padding: 6px 12px;
            border: 1px solid #c8e6c9;
        }
        
        .ingredient-tag.add-more {
            background: #f0f0f0;
            border: 1px solid #ddd;
        }
        
        .tag-text {
            font-size: 13px;
            color: #2e7d32;
        }
        
        .add-more .tag-text {
            color: #666;
        }
        
        /* 菜谱列表样式 */
        .recipe-list {
            padding: 0 8px;
        }
        
        .recipe-card {
            background: #fff;
            border-radius: 8px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.03);
            overflow: hidden;
        }
        
        /* 菜谱主要信息 */
        .recipe-main {
            display: flex;
            padding: 16px;
            gap: 12px;
        }
        
        .recipe-image {
            width: 60px;
            height: 60px;
            background: #ffeaa7;
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-shrink: 0;
        }
        
        .recipe-emoji {
            font-size: 24px;
        }
        
        .recipe-info {
            flex: 1;
            min-width: 0;
            overflow: hidden;
        }

        .recipe-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .recipe-description {
            font-size: 12px;
            color: #666;
            line-height: 1.3;
            margin-bottom: 6px;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .recipe-meta {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: nowrap;
            overflow: hidden;
        }
        
        .meta-time, .meta-rating, .meta-views {
            display: flex;
            align-items: center;
            gap: 2px;
            flex-shrink: 0;
            white-space: nowrap;
        }

        .time-icon, .rating-icon, .views-icon {
            font-size: 10px;
            flex-shrink: 0;
        }

        .time-text, .rating-text, .views-text {
            font-size: 10px;
            color: #666;
            flex-shrink: 0;
        }

        .meta-stars {
            margin-left: auto;
            flex-shrink: 0;
        }

        .stars-text {
            font-size: 10px;
            color: #ddd;
        }
        
        /* 食材匹配度 */
        .ingredient-match {
            padding: 12px 16px;
            border-top: 1px solid #f0f0f0;
        }
        
        .match-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .match-title {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }
        
        .match-ratio {
            font-size: 16px;
            color: #4caf50;
            font-weight: 600;
        }
        
        .match-progress {
            margin-bottom: 8px;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e0e0e0;
            border-radius: 3px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4caf50 0%, #8bc34a 100%);
            border-radius: 3px;
            transition: width 0.3s ease;
        }
        
        .match-details {
            display: flex;
            justify-content: space-between;
        }
        
        .match-have {
            font-size: 12px;
            color: #4caf50;
        }
        
        .match-need {
            font-size: 12px;
            color: #ff9800;
        }
        
        /* 查看详细做法按钮 */
        .recipe-action {
            padding: 12px 16px;
            border-top: 1px solid #f0f0f0;
        }
        
        .detail-btn {
            width: 100%;
            background: linear-gradient(90deg, #ff7e2d 0%, #ff3d2d 100%);
            color: #fff;
            font-size: 14px;
            font-weight: 500;
            border: none;
            border-radius: 6px;
            padding: 10px 0;
            text-align: center;
            box-shadow: 0 2px 8px rgba(255, 125, 45, 0.2);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .detail-btn:active {
            transform: scale(0.98);
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <!-- 自定义导航栏 -->
    <div class="custom-navbar">
        <div class="nav-back">
            <span class="nav-back-icon">←</span>
        </div>
        <div class="nav-title">推荐食谱</div>
        <div class="nav-help">
            <span class="help-icon">?</span>
        </div>
    </div>

    <!-- 已有食材标签 -->
    <div class="ingredients-section">
        <div class="ingredients-header">
            <span class="ingredients-label">已有食材：</span>
        </div>
        <div class="ingredients-tags">
            <div class="ingredient-tag">
                <span class="tag-text">西红柿</span>
            </div>
            <div class="ingredient-tag">
                <span class="tag-text">鸡蛋</span>
            </div>
            <div class="ingredient-tag">
                <span class="tag-text">大葱</span>
            </div>
            <div class="ingredient-tag add-more">
                <span class="tag-text">+1个</span>
            </div>
        </div>
    </div>

    <!-- 菜谱列表 -->
    <div class="recipe-list">
        <!-- 第一个菜谱 -->
        <div class="recipe-card">
            <!-- 菜谱图片和基本信息 -->
            <div class="recipe-main">
                <div class="recipe-image">
                    <span class="recipe-emoji">🍜</span>
                </div>
                <div class="recipe-info">
                    <div class="recipe-name">西红柿鸡蛋面</div>
                    <div class="recipe-description">经典家常面条，酸甜可口</div>
                    <div class="recipe-meta">
                        <div class="meta-time">
                            <span class="time-icon">⏰</span>
                            <span class="time-text">15分钟</span>
                        </div>
                        <div class="meta-rating">
                            <span class="rating-icon">⭐</span>
                            <span class="rating-text">4.8</span>
                        </div>
                        <div class="meta-views">
                            <span class="views-icon">👥</span>
                            <span class="views-text">1234</span>
                        </div>
                        <div class="meta-stars">
                            <span class="stars-text">☆☆☆☆☆</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 食材匹配度 -->
            <div class="ingredient-match">
                <div class="match-header">
                    <span class="match-title">食材匹配度</span>
                    <span class="match-ratio">4/6</span>
                </div>
                <div class="match-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 66.7%"></div>
                    </div>
                </div>
                <div class="match-details">
                    <span class="match-have">已有: 4样</span>
                    <span class="match-need">还需: 2样</span>
                </div>
            </div>

            <!-- 查看详细做法按钮 -->
            <div class="recipe-action">
                <button class="detail-btn">查看详细做法</button>
            </div>
        </div>

        <!-- 第二个菜谱 -->
        <div class="recipe-card">
            <!-- 菜谱图片和基本信息 -->
            <div class="recipe-main">
                <div class="recipe-image">
                    <span class="recipe-emoji">🍅</span>
                </div>
                <div class="recipe-info">
                    <div class="recipe-name">蒜蓉西红柿</div>
                    <div class="recipe-description">简单快手菜，开胃下饭</div>
                    <div class="recipe-meta">
                        <div class="meta-time">
                            <span class="time-icon">⏰</span>
                            <span class="time-text">10分钟</span>
                        </div>
                        <div class="meta-rating">
                            <span class="rating-icon">⭐</span>
                            <span class="rating-text">4.6</span>
                        </div>
                        <div class="meta-views">
                            <span class="views-icon">👥</span>
                            <span class="views-text">856</span>
                        </div>
                        <div class="meta-stars">
                            <span class="stars-text">☆☆☆☆☆</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 食材匹配度 -->
            <div class="ingredient-match">
                <div class="match-header">
                    <span class="match-title">食材匹配度</span>
                    <span class="match-ratio">3/5</span>
                </div>
                <div class="match-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 60%"></div>
                    </div>
                </div>
                <div class="match-details">
                    <span class="match-have">已有: 3样</span>
                    <span class="match-need">还需: 2样</span>
                </div>
            </div>

            <!-- 查看详细做法按钮 -->
            <div class="recipe-action">
                <button class="detail-btn">查看详细做法</button>
            </div>
        </div>

        <!-- 第三个菜谱 -->
        <div class="recipe-card">
            <div class="recipe-main">
                <div class="recipe-image">
                    <span class="recipe-emoji">🍲</span>
                </div>
                <div class="recipe-info">
                    <div class="recipe-name">西红柿鸡蛋汤</div>
                    <div class="recipe-description">清淡营养，老少皆宜的家常汤品</div>
                    <div class="recipe-meta">
                        <div class="meta-time">
                            <span class="time-icon">⏰</span>
                            <span class="time-text">8分钟</span>
                        </div>
                        <div class="meta-rating">
                            <span class="rating-icon">⭐</span>
                            <span class="rating-text">4.7</span>
                        </div>
                        <div class="meta-views">
                            <span class="views-icon">👥</span>
                            <span class="views-text">2156</span>
                        </div>
                        <div class="meta-stars">
                            <span class="stars-text">☆☆☆☆☆</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="ingredient-match">
                <div class="match-header">
                    <span class="match-title">食材匹配度</span>
                    <span class="match-ratio">3/4</span>
                </div>
                <div class="match-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 75%"></div>
                    </div>
                </div>
                <div class="match-details">
                    <span class="match-have">已有: 3样</span>
                    <span class="match-need">还需: 1样</span>
                </div>
            </div>
            <div class="recipe-action">
                <button class="detail-btn">查看详细做法</button>
            </div>
        </div>

        <!-- 第四个菜谱 -->
        <div class="recipe-card">
            <div class="recipe-main">
                <div class="recipe-image">
                    <span class="recipe-emoji">🥚</span>
                </div>
                <div class="recipe-info">
                    <div class="recipe-name">葱花炒鸡蛋</div>
                    <div class="recipe-description">简单易做，香嫩可口的下饭菜</div>
                    <div class="recipe-meta">
                        <div class="meta-time">
                            <span class="time-icon">⏰</span>
                            <span class="time-text">5分钟</span>
                        </div>
                        <div class="meta-rating">
                            <span class="rating-icon">⭐</span>
                            <span class="rating-text">4.5</span>
                        </div>
                        <div class="meta-views">
                            <span class="views-icon">👥</span>
                            <span class="views-text">987</span>
                        </div>
                        <div class="meta-stars">
                            <span class="stars-text">☆☆☆☆☆</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="ingredient-match">
                <div class="match-header">
                    <span class="match-title">食材匹配度</span>
                    <span class="match-ratio">2/3</span>
                </div>
                <div class="match-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 66.7%"></div>
                    </div>
                </div>
                <div class="match-details">
                    <span class="match-have">已有: 2样</span>
                    <span class="match-need">还需: 1样</span>
                </div>
            </div>
            <div class="recipe-action">
                <button class="detail-btn">查看详细做法</button>
            </div>
        </div>

        <!-- 第五个菜谱 -->
        <div class="recipe-card">
            <div class="recipe-main">
                <div class="recipe-image">
                    <span class="recipe-emoji">🥬</span>
                </div>
                <div class="recipe-info">
                    <div class="recipe-name">葱爆西红柿</div>
                    <div class="recipe-description">酸甜爽口，色泽鲜艳的素菜</div>
                    <div class="recipe-meta">
                        <div class="meta-time">
                            <span class="time-icon">⏰</span>
                            <span class="time-text">12分钟</span>
                        </div>
                        <div class="meta-rating">
                            <span class="rating-icon">⭐</span>
                            <span class="rating-text">4.4</span>
                        </div>
                        <div class="meta-views">
                            <span class="views-icon">👥</span>
                            <span class="views-text">654</span>
                        </div>
                        <div class="meta-stars">
                            <span class="stars-text">☆☆☆☆☆</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="ingredient-match">
                <div class="match-header">
                    <span class="match-title">食材匹配度</span>
                    <span class="match-ratio">2/4</span>
                </div>
                <div class="match-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 50%"></div>
                    </div>
                </div>
                <div class="match-details">
                    <span class="match-have">已有: 2样</span>
                    <span class="match-need">还需: 2样</span>
                </div>
            </div>
            <div class="recipe-action">
                <button class="detail-btn">查看详细做法</button>
            </div>
        </div>
    </div>
</body>
</html>
