/* pages/history/history.wxss */
.container {
  min-height: 100vh;
  background-color: #FFF6F0;
  padding: 24rpx 32rpx;
  box-sizing: border-box;
}

.history-list {
  width: 100%;
}

.history-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ffffff;
  padding: 32rpx;
  margin-bottom: 24rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.03);
  transition: transform 0.2s;
}

.history-card:active {
  transform: scale(0.98);
}

.card-left {
  display: flex;
  align-items: center;
}

.icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f2f3f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.icon-clock {
  font-size: 40rpx;
}

.card-info {
  display: flex;
  flex-direction: column;
}

.info-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #323233;
  margin-bottom: 8rpx;
}

.info-time {
  font-size: 24rpx;
  color: #969799;
}

.card-right {
  display: flex;
  align-items: center;
  color: #969799;
}

.info-confidence {
  font-size: 28rpx;
  font-weight: 500;
  color: #4caf50;
  margin-right: 16rpx;
}

.icon-arrow {
  font-size: 28rpx;
  font-weight: bold;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 200rpx;
  color: #969799;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 28rpx;
} 