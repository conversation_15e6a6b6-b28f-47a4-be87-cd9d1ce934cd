// index.js
Page({
  data: {
    recommendRecipes: [
      {
        id: 1,
        name: '清炒时蔬',
        image: '🍲',
        cookTime: 15,
        difficulty: '简单'
      },
      {
        id: 2,
        name: '红烧排骨',
        image: '🍲',
        cookTime: 45,
        difficulty: '中等'
      },
      {
        id: 3,
        name: '番茄炒蛋',
        image: '🍲',
        cookTime: 10,
        difficulty: '简单'
      },
      {
        id: 4,
        name: '糖醋里脊',
        image: '🍲',
        cookTime: 30,
        difficulty: '中等'
      }
    ],
    categories: [
      {
        id: 1,
        name: '蔬菜',
        icon: '🥦'
      },
      {
        id: 2,
        name: '肉类',
        icon: '🍖'
      },
      {
        id: 3,
        name: '海鲜',
        icon: '🦐'
      },
      {
        id: 4,
        name: '主食',
        icon: '🍚'
      },
      {
        id: 5,
        name: '水果',
        icon: '🍎'
      },
      {
        id: 6,
        name: '调味料',
        icon: '🧂'
      },
      {
        id: 7,
        name: '豆制品',
        icon: '🌱'
      },
      {
        id: 8,
        name: '更多',
        icon: '➕'
      }
    ]
  },

  onLoad() {
    // 页面加载时可以在这里请求推荐菜谱数据
  },

  // 导航到拍照页面
  navigateToCamera() {
    wx.navigateTo({
      url: '/pages/camera/camera'
    })
  },

  // 导航到菜谱列表页
  navigateToRecipe() {
    wx.navigateTo({
      url: '/pages/recipe/recipe'
    })
  },

  // 导航到菜谱详情页
  navigateToDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/recipe-detail/recipe-detail?id=${id}`
    })
  },

  // 导航到分类页面
  navigateToCategory(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/recipe/recipe?category=${id}`
    })
  },

  // 搜索功能
  onSearch(e) {
    const keyword = e && e.detail && e.detail.value
    if (keyword) {
      wx.navigateTo({
        url: `/pages/recipe/recipe?keyword=${encodeURIComponent(keyword)}`
      })
    } else {
      // 如果没有输入，跳转到搜索页
      wx.navigateTo({
        url: '/pages/recipe/recipe'
      })
    }
  },

  // 跳转到收藏页面
  navigateToFavorite() {
    wx.navigateTo({
      url: '/pages/profile/profile'
    })
  }
})
