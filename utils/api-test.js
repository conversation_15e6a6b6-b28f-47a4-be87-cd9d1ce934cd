// API测试工具
// 用于测试 /cook/upload/photos 接口的请求格式

/**
 * 测试用的Base64图片数据（1x1像素的JPEG图片）
 */
const testBase64Image = '/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8A8A';

/**
 * 创建符合接口文档要求的测试数据
 */
function createTestUploadData() {
  const timestamp = new Date().getTime();
  
  return {
    files: [
      {
        fileName: `test_image_${timestamp}.jpg`,
        fileId: timestamp,
        fileSize: 1024, // 假设文件大小为1KB
        fileData: `data:image/jpeg;base64,${testBase64Image}`
      }
    ]
  };
}

/**
 * 测试API请求格式
 * @param {Object} apiConfig - API配置对象
 * @returns {Promise} 请求结果
 */
function testUploadPhotosAPI(apiConfig) {
  const testData = createTestUploadData();
  
  console.log('测试数据格式:', JSON.stringify(testData, null, 2));
  
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${apiConfig.baseUrl}${apiConfig.api.uploadPhotos}`,
      method: 'POST',
      data: testData,
      header: {
        'content-type': 'application/json',
        'accept': 'application/json'
      },
      success: (res) => {
        console.log('API测试成功:', res);
        resolve(res);
      },
      fail: (error) => {
        console.error('API测试失败:', error);
        reject(error);
      }
    });
  });
}

/**
 * 验证请求数据格式是否符合接口文档
 * @param {Object} uploadData - 上传数据
 * @returns {Object} 验证结果
 */
function validateUploadData(uploadData) {
  const errors = [];
  
  // 检查根级别的files字段
  if (!uploadData.files || !Array.isArray(uploadData.files)) {
    errors.push('缺少files字段或files不是数组');
    return { valid: false, errors };
  }
  
  // 检查每个文件对象
  uploadData.files.forEach((file, index) => {
    const prefix = `files[${index}]`;
    
    // 必填字段检查
    if (!file.fileName) {
      errors.push(`${prefix}.fileName 是必填字段`);
    }
    
    if (!file.fileSize || typeof file.fileSize !== 'number' || file.fileSize <= 0) {
      errors.push(`${prefix}.fileSize 必须是大于0的数字`);
    }
    
    if (!file.fileData) {
      errors.push(`${prefix}.fileData 是必填字段`);
    } else if (!file.fileData.startsWith('data:image/')) {
      errors.push(`${prefix}.fileData 必须是完整的Data URL格式，以"data:image/"开头`);
    }
    
    // 可选字段检查
    if (file.fileId !== undefined && typeof file.fileId !== 'number') {
      errors.push(`${prefix}.fileId 如果提供，应该是数字类型`);
    }
  });
  
  return {
    valid: errors.length === 0,
    errors
  };
}

module.exports = {
  createTestUploadData,
  testUploadPhotosAPI,
  validateUploadData,
  testBase64Image
};
