// 微信登录功能测试工具
const authManager = require('./auth.js');

/**
 * 测试微信登录功能
 */
async function testWxLogin() {
  console.log('=== 开始测试微信登录功能 ===');
  
  try {
    // 1. 检查初始登录状态
    console.log('1. 检查初始登录状态');
    const initialStatus = authManager.checkLoginStatus();
    console.log('初始登录状态:', initialStatus);
    console.log('初始用户信息:', authManager.getCurrentUser());
    
    // 2. 执行登录
    console.log('2. 执行微信登录');
    const loginResult = await authManager.wxLogin();
    console.log('登录结果:', loginResult);
    
    if (loginResult.success) {
      // 3. 检查登录后状态
      console.log('3. 检查登录后状态');
      const afterLoginStatus = authManager.checkLoginStatus();
      console.log('登录后状态:', afterLoginStatus);
      console.log('登录后用户信息:', authManager.getCurrentUser());
      console.log('登录后token:', authManager.getToken());
      
      // 4. 测试认证头
      console.log('4. 测试认证头');
      const authHeaders = authManager.getAuthHeaders();
      console.log('认证头:', authHeaders);
      
      return {
        success: true,
        message: '微信登录测试成功'
      };
    } else {
      return {
        success: false,
        message: '微信登录测试失败: ' + loginResult.message
      };
    }
  } catch (error) {
    console.error('微信登录测试出错:', error);
    return {
      success: false,
      message: '微信登录测试出错: ' + error.message
    };
  }
}

/**
 * 测试登录状态恢复
 */
function testLoginStateRestore() {
  console.log('=== 开始测试登录状态恢复 ===');
  
  try {
    // 模拟保存登录信息
    const mockUserInfo = {
      nickName: '测试用户',
      avatarUrl: 'https://example.com/avatar.jpg',
      gender: 1,
      country: '中国',
      province: '广东省',
      city: '深圳市',
      language: 'zh_CN'
    };
    
    const mockToken = 'mock_token_12345';
    
    // 保存到本地存储
    wx.setStorageSync('userInfo', mockUserInfo);
    wx.setStorageSync('token', mockToken);
    wx.setStorageSync('isLoggedIn', true);
    
    console.log('1. 模拟登录信息已保存到本地存储');
    
    // 恢复登录状态
    const restoreResult = authManager.restoreLoginState();
    console.log('2. 恢复登录状态结果:', restoreResult);
    
    // 检查恢复后的状态
    console.log('3. 检查恢复后的状态');
    console.log('登录状态:', authManager.checkLoginStatus());
    console.log('用户信息:', authManager.getCurrentUser());
    console.log('token:', authManager.getToken());
    
    return {
      success: restoreResult,
      message: restoreResult ? '登录状态恢复测试成功' : '登录状态恢复测试失败'
    };
  } catch (error) {
    console.error('登录状态恢复测试出错:', error);
    return {
      success: false,
      message: '登录状态恢复测试出错: ' + error.message
    };
  }
}

/**
 * 测试退出登录
 */
function testLogout() {
  console.log('=== 开始测试退出登录 ===');
  
  try {
    // 检查退出前状态
    console.log('1. 退出前状态');
    console.log('登录状态:', authManager.checkLoginStatus());
    console.log('用户信息:', authManager.getCurrentUser());
    
    // 执行退出登录
    const logoutResult = authManager.logout();
    console.log('2. 退出登录结果:', logoutResult);
    
    // 检查退出后状态
    console.log('3. 退出后状态');
    console.log('登录状态:', authManager.checkLoginStatus());
    console.log('用户信息:', authManager.getCurrentUser());
    console.log('token:', authManager.getToken());
    
    // 检查本地存储是否清除
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');
    const isLoggedIn = wx.getStorageSync('isLoggedIn');
    
    console.log('4. 本地存储状态');
    console.log('userInfo:', userInfo);
    console.log('token:', token);
    console.log('isLoggedIn:', isLoggedIn);
    
    const isCleared = !userInfo && !token && !isLoggedIn;
    
    return {
      success: logoutResult && isCleared,
      message: (logoutResult && isCleared) ? '退出登录测试成功' : '退出登录测试失败'
    };
  } catch (error) {
    console.error('退出登录测试出错:', error);
    return {
      success: false,
      message: '退出登录测试出错: ' + error.message
    };
  }
}

/**
 * 模拟API响应测试
 */
function testMockAPIResponse() {
  console.log('=== 开始测试模拟API响应 ===');
  
  // 模拟成功响应
  const mockSuccessResponse = {
    statusCode: 200,
    data: {
      code: 0,
      msg: '登录成功',
      data: {
        userInfo: {
          id: 12345,
          nickName: '测试用户',
          avatarUrl: 'https://example.com/avatar.jpg',
          gender: 1,
          country: '中国',
          province: '广东省',
          city: '深圳市',
          language: 'zh_CN'
        },
        token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        expiresIn: 7200
      }
    }
  };
  
  console.log('模拟成功响应:', mockSuccessResponse);
  
  // 模拟失败响应
  const mockErrorResponse = {
    statusCode: 200,
    data: {
      code: 1,
      msg: '登录失败，用户信息无效',
      data: null
    }
  };
  
  console.log('模拟失败响应:', mockErrorResponse);
  
  return {
    success: true,
    message: '模拟API响应测试完成',
    data: {
      successResponse: mockSuccessResponse,
      errorResponse: mockErrorResponse
    }
  };
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🧪 开始运行微信登录功能完整测试');
  
  const results = [];
  
  // 1. 测试登录状态恢复
  const restoreTest = testLoginStateRestore();
  results.push({ name: '登录状态恢复', ...restoreTest });
  
  // 2. 测试退出登录
  const logoutTest = testLogout();
  results.push({ name: '退出登录', ...logoutTest });
  
  // 3. 测试模拟API响应
  const mockTest = testMockAPIResponse();
  results.push({ name: '模拟API响应', ...mockTest });
  
  // 4. 测试微信登录（需要用户交互，在实际环境中测试）
  console.log('⚠️ 微信登录测试需要在真实环境中进行用户交互测试');
  
  // 输出测试结果
  console.log('📊 测试结果汇总:');
  results.forEach((result, index) => {
    const status = result.success ? '✅' : '❌';
    console.log(`${index + 1}. ${status} ${result.name}: ${result.message}`);
  });
  
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  console.log(`\n🎯 测试完成: ${successCount}/${totalCount} 通过`);
  
  return {
    success: successCount === totalCount,
    results: results,
    summary: `${successCount}/${totalCount} 测试通过`
  };
}

module.exports = {
  testWxLogin,
  testLoginStateRestore,
  testLogout,
  testMockAPIResponse,
  runAllTests
};
