// 用户认证管理工具
const apiConfig = require('../config/api.js');

/**
 * 用户认证管理类
 */
class AuthManager {
  constructor() {
    this.userInfo = null;
    this.token = null;
    this.isLoggedIn = false;
  }

  /**
   * 微信小程序登录 - 需要在用户点击事件中调用
   * @param {object} userInfo 用户信息（从wx.getUserProfile获取）
   * @returns {Promise} 登录结果
   */
  async wxLogin(userInfo) {
    try {
      // 1. 获取微信登录凭证
      const loginResult = await this.getWxLoginCode();
      if (!loginResult.code) {
        throw new Error('获取微信登录凭证失败');
      }

      // 2. 调用后端登录接口
      const loginResponse = await this.callLoginAPI(loginResult.code, userInfo);

      // 3. 保存登录信息
      await this.saveLoginInfo(loginResponse);

      return {
        success: true,
        data: loginResponse,
        message: '登录成功'
      };
    } catch (error) {
      console.error('微信登录失败:', error);
      return {
        success: false,
        error: error.message || '登录失败',
        message: error.message || '登录失败'
      };
    }
  }

  /**
   * 获取用户信息 - 必须在用户点击事件中调用
   * @returns {Promise} 用户信息
   */
  getUserProfileInTapEvent() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          console.log('获取用户信息成功:', res.userInfo);
          resolve(res.userInfo);
        },
        fail: (error) => {
          console.error('获取用户信息失败:', error);
          reject(new Error(error.errMsg || '获取用户信息失败'));
        }
      });
    });
  }

  /**
   * 获取微信登录凭证
   * @returns {Promise} 登录凭证
   */
  getWxLoginCode() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            console.log('获取微信登录凭证成功:', res.code);
            resolve(res);
          } else {
            console.error('获取微信登录凭证失败:', res.errMsg);
            reject(new Error(res.errMsg || '获取登录凭证失败'));
          }
        },
        fail: (error) => {
          console.error('wx.login调用失败:', error);
          reject(new Error(error.errMsg || 'wx.login调用失败'));
        }
      });
    });
  }



  /**
   * 调用后端登录接口
   * @param {string} code 微信登录凭证
   * @param {object} userInfo 用户信息
   * @returns {Promise} 登录响应
   */
  callLoginAPI(code, userInfo) {
    return new Promise((resolve, reject) => {
      const requestData = {
        code: code,
        userInfo: userInfo
      };

      console.log('调用登录接口，请求数据:', requestData);

      wx.request({
        url: `${apiConfig.baseUrl}${apiConfig.api.wxLogin}`,
        method: 'POST',
        data: requestData,
        header: {
          'content-type': 'application/json'
        },
        success: (res) => {
          console.log('登录接口响应:', res);
          
          if (res.statusCode === 200 && res.data && res.data.code === 0) {
            resolve(res.data.data);
          } else {
            const errorMsg = res.data && res.data.msg ? res.data.msg : '登录失败';
            reject(new Error(errorMsg));
          }
        },
        fail: (error) => {
          console.error('登录接口调用失败:', error);
          reject(new Error('网络错误，请重试'));
        }
      });
    });
  }

  /**
   * 保存登录信息到本地存储
   * @param {object} loginData 登录返回的数据
   */
  async saveLoginInfo(loginData) {
    try {
      console.log('保存登录信息，原始数据:', loginData);

      // 保存用户信息 - 适配后端返回的数据结构
      if (loginData.userInfo) {
        // 转换后端用户信息格式为前端期望的格式
        const userInfo = {
          id: loginData.userInfo.id,
          nickName: loginData.userInfo.nickname || '微信用户',
          avatarUrl: loginData.userInfo.avatarUrl || '',
          gender: loginData.userInfo.gender || 0,
          // 保留原始的后端用户信息
          ...loginData.userInfo
        };

        this.userInfo = userInfo;
        wx.setStorageSync('userInfo', userInfo);
        console.log('用户信息已保存:', userInfo);
      }

      // 保存token
      if (loginData.token) {
        this.token = loginData.token;
        wx.setStorageSync('token', loginData.token);
        console.log('Token已保存');
      }

      // 保存用户ID
      if (loginData.userId) {
        wx.setStorageSync('userId', loginData.userId);
        console.log('用户ID已保存:', loginData.userId);
      }

      // 保存过期时间
      if (loginData.expireTime) {
        wx.setStorageSync('tokenExpireTime', loginData.expireTime);
        console.log('Token过期时间已保存:', new Date(loginData.expireTime));
      }

      // 保存登录状态
      this.isLoggedIn = true;
      wx.setStorageSync('isLoggedIn', true);

      console.log('登录信息保存成功');
    } catch (error) {
      console.error('保存登录信息失败:', error);
      throw new Error('保存登录信息失败');
    }
  }

  /**
   * 从本地存储恢复登录状态
   */
  restoreLoginState() {
    try {
      const isLoggedIn = wx.getStorageSync('isLoggedIn');
      const userInfo = wx.getStorageSync('userInfo');
      const token = wx.getStorageSync('token');
      const userId = wx.getStorageSync('userId');
      const tokenExpireTime = wx.getStorageSync('tokenExpireTime');

      console.log('恢复登录状态，本地数据:', {
        isLoggedIn,
        userInfo,
        token: token ? '已存在' : '不存在',
        userId,
        tokenExpireTime: tokenExpireTime ? new Date(tokenExpireTime) : '无'
      });

      // 检查token是否过期
      if (tokenExpireTime && Date.now() > tokenExpireTime) {
        console.log('Token已过期，清除登录状态');
        this.logout();
        return false;
      }

      if (isLoggedIn && userInfo && token) {
        this.isLoggedIn = true;
        this.userInfo = userInfo;
        this.token = token;
        console.log('登录状态恢复成功，用户信息:', userInfo);
        return true;
      } else {
        console.log('无有效的登录状态');
        return false;
      }
    } catch (error) {
      console.error('恢复登录状态失败:', error);
      return false;
    }
  }

  /**
   * 退出登录
   */
  logout() {
    try {
      // 清除内存中的数据
      this.userInfo = null;
      this.token = null;
      this.isLoggedIn = false;

      // 清除本地存储
      wx.removeStorageSync('userInfo');
      wx.removeStorageSync('token');
      wx.removeStorageSync('isLoggedIn');
      wx.removeStorageSync('userId');
      wx.removeStorageSync('tokenExpireTime');

      console.log('退出登录成功');
      return true;
    } catch (error) {
      console.error('退出登录失败:', error);
      return false;
    }
  }

  /**
   * 检查登录状态
   * @returns {boolean} 是否已登录
   */
  checkLoginStatus() {
    return this.isLoggedIn && this.userInfo && this.token;
  }

  /**
   * 获取当前用户信息
   * @returns {object|null} 用户信息
   */
  getCurrentUser() {
    return this.userInfo;
  }

  /**
   * 获取当前token
   * @returns {string|null} token
   */
  getToken() {
    return this.token;
  }

  /**
   * 获取带认证头的请求配置
   * @returns {object} 请求头配置
   */
  getAuthHeaders() {
    const headers = {
      'content-type': 'application/json'
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    return headers;
  }
}

// 创建全局实例
const authManager = new AuthManager();

// 应用启动时恢复登录状态
authManager.restoreLoginState();

module.exports = authManager;
