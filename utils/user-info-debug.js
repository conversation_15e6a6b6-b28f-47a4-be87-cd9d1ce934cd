// 用户信息调试工具
const authManager = require('./auth.js');

/**
 * 调试用户信息传递流程
 */
function debugUserInfoFlow() {
  console.log('=== 用户信息调试工具 ===');
  
  // 模拟微信返回的真实用户信息
  const mockWxUserInfo = {
    nickName: "真实微信昵称",
    avatarUrl: "https://wx.qlogo.cn/mmopen/vi_32/real_avatar_url/132",
    gender: 1,
    country: "中国",
    province: "广东省",
    city: "深圳市",
    language: "zh_CN"
  };
  
  // 模拟后端返回的数据（可能是默认数据）
  const mockBackendResponse = {
    userId: 1,
    token: "mock_token_123",
    expireTime: Date.now() + 7 * 24 * 60 * 60 * 1000,
    userInfo: {
      id: 1,
      nickname: "微信用户", // 后端默认昵称
      avatarUrl: "https://thirdwx.qlogo.cn/mmopen/vi_32/default_avatar/132", // 后端默认头像
      gender: 0,
      status: 0,
      lastLoginTime: Date.now(),
      createTime: Date.now()
    }
  };
  
  console.log('1. 微信真实用户信息:', mockWxUserInfo);
  console.log('2. 后端返回数据:', mockBackendResponse);
  
  // 测试数据合并逻辑
  const finalUserInfo = mergeUserInfo(mockBackendResponse, mockWxUserInfo);
  console.log('3. 最终用户信息:', finalUserInfo);
  
  // 验证关键字段
  console.log('4. 关键字段验证:');
  console.log('   - 昵称来源:', finalUserInfo.nickName === mockWxUserInfo.nickName ? '微信真实' : '后端默认');
  console.log('   - 头像来源:', finalUserInfo.avatarUrl === mockWxUserInfo.avatarUrl ? '微信真实' : '后端默认');
  
  return finalUserInfo;
}

/**
 * 合并用户信息的逻辑（模拟认证管理器的处理）
 */
function mergeUserInfo(backendData, wxUserInfo) {
  if (wxUserInfo) {
    // 优先使用微信真实用户信息
    return {
      id: backendData.userInfo ? backendData.userInfo.id : null,
      nickName: wxUserInfo.nickName || '微信用户',
      avatarUrl: wxUserInfo.avatarUrl || '',
      gender: wxUserInfo.gender || 0,
      country: wxUserInfo.country || '',
      province: wxUserInfo.province || '',
      city: wxUserInfo.city || '',
      language: wxUserInfo.language || 'zh_CN',
      // 保留后端用户信息
      ...(backendData.userInfo || {})
    };
  } else {
    // 使用后端返回的信息
    return {
      id: backendData.userInfo.id,
      nickName: backendData.userInfo.nickname || '微信用户',
      avatarUrl: backendData.userInfo.avatarUrl || '',
      gender: backendData.userInfo.gender || 0,
      ...backendData.userInfo
    };
  }
}

/**
 * 检查当前保存的用户信息
 */
function checkCurrentUserInfo() {
  console.log('\n=== 检查当前用户信息 ===');
  
  try {
    // 检查认证管理器中的用户信息
    const authUserInfo = authManager.getCurrentUser();
    console.log('认证管理器中的用户信息:', authUserInfo);
    
    // 检查本地存储中的用户信息
    const storageUserInfo = wx.getStorageSync('userInfo');
    console.log('本地存储中的用户信息:', storageUserInfo);
    
    // 检查登录状态
    const isLoggedIn = authManager.checkLoginStatus();
    console.log('登录状态:', isLoggedIn);
    
    // 分析用户信息来源
    if (authUserInfo) {
      console.log('\n用户信息分析:');
      console.log('- 昵称:', authUserInfo.nickName);
      console.log('- 头像URL:', authUserInfo.avatarUrl);
      console.log('- 是否为默认昵称:', authUserInfo.nickName === '微信用户');
      console.log('- 头像是否为微信真实头像:', authUserInfo.avatarUrl && authUserInfo.avatarUrl.includes('wx.qlogo.cn'));
    }
    
    return {
      authUserInfo,
      storageUserInfo,
      isLoggedIn
    };
  } catch (error) {
    console.error('检查用户信息失败:', error);
    return null;
  }
}

/**
 * 模拟完整的登录流程
 */
async function simulateLoginFlow() {
  console.log('\n=== 模拟完整登录流程 ===');
  
  // 1. 模拟微信用户授权
  const wxUserInfo = {
    nickName: "张三",
    avatarUrl: "https://wx.qlogo.cn/mmopen/vi_32/real_user_avatar/132",
    gender: 1,
    country: "中国",
    province: "广东省",
    city: "深圳市",
    language: "zh_CN"
  };
  
  console.log('1. 用户授权获取的信息:', wxUserInfo);
  
  // 2. 模拟后端API响应
  const backendResponse = {
    userId: 123,
    token: "real_jwt_token",
    expireTime: Date.now() + 7 * 24 * 60 * 60 * 1000,
    userInfo: {
      id: 123,
      nickname: "微信用户", // 后端可能返回默认值
      avatarUrl: "https://default.avatar.url",
      gender: 0,
      status: 0,
      lastLoginTime: Date.now(),
      createTime: Date.now()
    }
  };
  
  console.log('2. 后端API响应:', backendResponse);
  
  // 3. 模拟保存登录信息
  try {
    await authManager.saveLoginInfo(backendResponse, wxUserInfo);
    console.log('3. 登录信息保存完成');
    
    // 4. 检查最终结果
    const finalUserInfo = authManager.getCurrentUser();
    console.log('4. 最终用户信息:', finalUserInfo);
    
    // 5. 验证结果
    const isCorrect = finalUserInfo.nickName === wxUserInfo.nickName && 
                     finalUserInfo.avatarUrl === wxUserInfo.avatarUrl;
    
    console.log('5. 验证结果:', isCorrect ? '✅ 使用了微信真实信息' : '❌ 使用了默认信息');
    
    return {
      success: isCorrect,
      userInfo: finalUserInfo
    };
  } catch (error) {
    console.error('模拟登录流程失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 生成用户信息对比报告
 */
function generateUserInfoReport() {
  console.log('\n=== 用户信息对比报告 ===');
  
  const currentInfo = checkCurrentUserInfo();
  
  if (currentInfo && currentInfo.authUserInfo) {
    const userInfo = currentInfo.authUserInfo;
    
    console.log('📊 用户信息状态:');
    console.log(`昵称: ${userInfo.nickName} ${userInfo.nickName === '微信用户' ? '(默认)' : '(真实)'}`);
    console.log(`头像: ${userInfo.avatarUrl ? '已设置' : '未设置'}`);
    console.log(`性别: ${userInfo.gender === 1 ? '男' : userInfo.gender === 2 ? '女' : '未知'}`);
    console.log(`地区: ${userInfo.country || '未知'} ${userInfo.province || ''} ${userInfo.city || ''}`);
    
    // 判断数据来源
    const isRealWxData = userInfo.nickName !== '微信用户' && 
                        userInfo.avatarUrl && 
                        !userInfo.avatarUrl.includes('default');
    
    console.log(`\n📈 数据质量: ${isRealWxData ? '✅ 真实微信数据' : '⚠️ 默认测试数据'}`);
    
    if (!isRealWxData) {
      console.log('\n💡 建议:');
      console.log('1. 检查前端是否正确传递了微信用户信息');
      console.log('2. 检查后端是否正确处理了用户信息');
      console.log('3. 确认认证管理器的数据合并逻辑');
    }
  } else {
    console.log('❌ 未找到用户信息');
  }
}

/**
 * 运行所有调试测试
 */
async function runAllDebugTests() {
  console.log('🔍 开始用户信息调试测试\n');
  
  // 1. 调试用户信息流程
  debugUserInfoFlow();
  
  // 2. 检查当前用户信息
  checkCurrentUserInfo();
  
  // 3. 模拟登录流程
  await simulateLoginFlow();
  
  // 4. 生成对比报告
  generateUserInfoReport();
  
  console.log('\n🎯 调试测试完成');
  console.log('请检查上述输出，确认用户信息是否正确处理');
}

module.exports = {
  debugUserInfoFlow,
  checkCurrentUserInfo,
  simulateLoginFlow,
  generateUserInfoReport,
  runAllDebugTests
};
