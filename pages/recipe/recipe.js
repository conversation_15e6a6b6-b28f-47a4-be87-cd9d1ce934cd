// pages/recipe/recipe.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    keyword: '',
    currentFilter: 'all',
    ingredients: [],
    recipes: [],
    page: 1,
    hasMore: true,
    isLoading: false,
    resultList: [] // 识别结果列表
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 优先处理result参数（识别结果页面跳转传递）
    if (options.result) {
      try {
        let resultList = JSON.parse(decodeURIComponent(options.result));
        // 初始化每项的editing为false
        resultList = resultList.map(item => ({ ...item, editing: false }));
        this.setData({ resultList });
      } catch (e) {
        console.error('解析result参数失败', e);
      }
    } else if (options.files) {
      // 兼容老逻辑
      try {
        const files = JSON.parse(decodeURIComponent(options.files));
        this.fetchRecognitionResult(files);
      } catch (e) {
        console.error('解析files参数失败', e);
      }
    }

    // 处理从拍照页面传递过来的食材数据
    if (options.ingredients) {
      try {
        const ingredients = JSON.parse(decodeURIComponent(options.ingredients))
        this.setData({ ingredients })
      } catch (error) {
        console.error('解析食材数据失败：', error)
      }
    }

    // 处理搜索关键词
    if (options.keyword) {
      this.setData({
        keyword: decodeURIComponent(options.keyword)
      })
    }

    // 处理分类筛选
    if (options.category) {
      this.setData({
        currentFilter: options.category
      })
    }

    // 加载菜谱数据
    this.loadRecipes()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 加载菜谱数据
  loadRecipes() {
    if (this.data.isLoading || !this.data.hasMore) return

    this.setData({ isLoading: true })

    // 这里应该调用实际的API获取菜谱数据
    // 示例数据
    setTimeout(() => {
      const mockRecipes = [
        {
          id: 1,
          name: '清炒时蔬',
          image: '/assets/images/recipe1.jpg',
          cookTime: 15,
          difficulty: '简单',
          tags: ['快手菜', '素菜']
        },
        {
          id: 2,
          name: '红烧排骨',
          image: '/assets/images/recipe2.jpg',
          cookTime: 45,
          difficulty: '中等',
          tags: ['家常菜', '肉类']
        },
        {
          id: 3,
          name: '番茄炒蛋',
          image: '/assets/images/recipe3.jpg',
          cookTime: 10,
          difficulty: '简单',
          tags: ['快手菜', '家常菜']
        },
        {
          id: 4,
          name: '糖醋里脊',
          image: '/assets/images/recipe4.jpg',
          cookTime: 30,
          difficulty: '中等',
          tags: ['家常菜', '肉类']
        }
      ]

      // 模拟分页
      const isLastPage = this.data.page >= 3
      const newRecipes = this.data.page === 1 ? mockRecipes : [...this.data.recipes, ...mockRecipes]

      this.setData({
        recipes: newRecipes,
        page: this.data.page + 1,
        hasMore: !isLastPage,
        isLoading: false
      })
    }, 1000)
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      keyword: e.detail.value
    })
  },

  // 执行搜索
  onSearch() {
    this.setData({
      recipes: [],
      page: 1,
      hasMore: true
    })
    this.loadRecipes()
  },

  // 设置筛选条件
  setFilter(e) {
    const { filter } = e.currentTarget.dataset
    this.setData({
      currentFilter: filter,
      recipes: [],
      page: 1,
      hasMore: true
    })
    this.loadRecipes()
  },

  // 移除食材标签
  removeIngredient(e) {
    const { index } = e.currentTarget.dataset
    const ingredients = [...this.data.ingredients]
    ingredients.splice(index, 1)
    this.setData({
      ingredients,
      recipes: [],
      page: 1,
      hasMore: true
    })
    this.loadRecipes()
  },

  // 加载更多
  loadMore() {
    // 如果有识别结果，说明是从食材搜索菜谱，需要加载更多菜谱数据
    if (this.data.resultList && this.data.resultList.length > 0) {
      const ingredientNames = this.data.resultList.map(item => item.name).join(',');
      const currentPage = Math.floor(this.data.recipes.length / 10) + 1; // 计算当前页码
      this.searchRecipesByIngredients(ingredientNames, currentPage, 10);
    } else {
      // 否则加载模拟数据
      this.loadRecipes();
    }
  },

  // 跳转到菜谱详情
  navigateToDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/recipe-detail/recipe-detail?id=${id}`
    })
  },

  // 跳转到拍照页面
  navigateToCamera() {
    wx.redirectTo({ url: '/pages/camera/camera' });
  },

  // 获取识别结果
  fetchRecognitionResult(files) {
    const apiConfig = require('../../config/api.js');
    console.log('准备上传的files参数:', files);
    wx.request({
      url: `${apiConfig.baseUrl}${apiConfig.api.uploadPhotos}`,
      method: 'POST',
      data: { files },
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        console.log('接口success回调:', res);
        if (res.data && typeof res.data === 'string') {
          try {
            res.data = JSON.parse(res.data);
          } catch (e) {
            console.error('返回数据不是JSON格式:', res.data);
          }
        }
        if (res.data && res.data.code === 0) {
          this.setData({ resultList: res.data.data });
        } else {
          wx.showToast({ title: res.data && res.data.msg ? res.data.msg : '识别失败', icon: 'none' });
        }
      },
      fail: (err) => {
        console.log('接口fail回调:', err);
        wx.showToast({ title: '识别失败', icon: 'none' });
      },
      complete: (res) => {
        console.log('接口complete回调:', res);
      }
    });
  },

  // 点击食材名进入编辑
  onEditIngredient(e) {
    const index = e.currentTarget.dataset.index;
    console.log('edit', index);
    const resultList = this.data.resultList.map((item, i) => ({
      ...item,
      editing: i === index
    }));
    this.setData({ resultList });
  },

  // 编辑完成（失焦或回车）
  onEditIngredientBlur(e) {
    this._saveIngredientEdit(e);
  },

  onEditIngredientConfirm(e) {
    this._saveIngredientEdit(e);
  },

  _saveIngredientEdit(e) {
    const index = e.currentTarget.dataset.index;
    const value = e.detail.value.trim();
    const resultList = this.data.resultList.map((item, i) => {
      if (i === index) {
        return {
          ...item,
          name: value || item.name,
          rate: '100%',
          editing: false
        };
      }
      return { ...item, editing: false };
    });
    this.setData({ resultList });
  },

  // 查看推荐食谱
  onShowRecipe() {
    const { resultList } = this.data;

    if (!resultList || resultList.length === 0) {
      wx.showToast({
        title: '请先识别食材',
        icon: 'none'
      });
      return;
    }

    // 提取食材名称
    const ingredientNames = resultList.map(item => item.name).join(',');

    // 调用搜索菜谱接口
    this.searchRecipesByIngredients(ingredientNames, 1, 10);
  },

  // 根据食材搜索菜谱
  searchRecipesByIngredients(ingredientNames, pageNum = 1, pageSize = 10) {
    const apiConfig = require('../../config/api.js');

    // 显示加载状态
    wx.showLoading({
      title: '搜索菜谱中...'
    });

    // 构建请求URL
    const url = `${apiConfig.baseUrl}${apiConfig.api.searchRecipesByIngredients}?ingredientNames=${encodeURIComponent(ingredientNames)}&pageNum=${pageNum}&pageSize=${pageSize}`;

    console.log('搜索菜谱请求URL:', url);

    wx.request({
      url: url,
      method: 'GET',
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        wx.hideLoading();
        console.log('搜索菜谱响应:', res);

        if (res.statusCode === 200 && res.data && res.data.code === 200) {
          const recipeData = res.data.data;

          if (recipeData.list && recipeData.list.length > 0) {
            // 判断是第一页还是加载更多
            const isFirstPage = pageNum === 1;
            const currentRecipes = isFirstPage ? [] : this.data.recipes;

            // 更新菜谱列表数据
            this.setData({
              recipes: [...currentRecipes, ...recipeData.list],
              page: recipeData.pageNum + 1,
              hasMore: recipeData.hasNext,
              isLoading: false
            });

            // 显示成功提示（仅第一页）
            if (isFirstPage) {
              wx.showToast({
                title: `找到${recipeData.total}个菜谱`,
                icon: 'success'
              });
            }
          } else {
            // 没有找到相关菜谱
            if (pageNum === 1) {
              this.setData({
                recipes: [],
                hasMore: false,
                isLoading: false
              });

              wx.showToast({
                title: '暂无相关菜谱',
                icon: 'none'
              });
            } else {
              // 加载更多时没有数据
              this.setData({
                hasMore: false,
                isLoading: false
              });
            }
          }
        } else {
          console.error('搜索菜谱失败，状态码:', res.statusCode);
          console.error('响应数据:', res.data);

          wx.showToast({
            title: res.data && res.data.msg ? res.data.msg : '搜索失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        wx.hideLoading();
        console.error('搜索菜谱失败:', error);

        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  }
})