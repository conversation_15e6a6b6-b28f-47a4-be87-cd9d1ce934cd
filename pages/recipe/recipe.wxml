<!--pages/recipe/recipe.wxml-->
<view class="container">
  <!-- 顶部栏 -->
  <view class="result-header">
    <text class="retake-btn-center" bindtap="navigateToCamera">重拍</text>
  </view>

  <!-- 识别结果展示 -->
  <view class="result-card" wx:if="{{resultList.length > 0}}">
    <view class="result-title">识别到的食材</view>
    <block wx:for="{{resultList}}" wx:for-item="item" wx:for-index="index" wx:key="index">
      <view class="ingredient-item">
        <view class="ingredient-left">
          <text class="check-icon">✔️</text>
          <block wx:if="!item.editing">
            <view class="ingredient-name" bindtap="onEditIngredient" data-index="{{index}}">{{item.name}}</view>
          </block>
          <block wx:else>
            <input class="ingredient-edit-input" value="{{item.name}}" focus="true" bindblur="onEditIngredientBlur" bindconfirm="onEditIngredientConfirm" data-index="{{index}}" />
          </block>
        </view>
        <text class="ingredient-rate">{{item.rate}}</text>
      </view>
    </block>
    <button class="btn-recipe" bindtap="onShowRecipe">查看推荐食谱 ({{resultList.length}})</button>
  </view>

  <!-- 推荐菜谱列表 -->
  <view class="recipe-list-section" wx:if="{{recipes.length > 0}}">
    <view class="section-title">推荐菜谱</view>
    <view class="recipe-list">
      <view class="recipe-card" wx:for="{{recipes}}" wx:key="id" bindtap="navigateToDetail" data-id="{{item.id}}">
        <view class="recipe-header">
          <text class="recipe-name">{{item.name}}</text>
          <view class="recipe-difficulty">
            <text class="difficulty-text">{{item.difficultyLevelDesc}}</text>
          </view>
        </view>
        <view class="recipe-description">{{item.description}}</view>
        <view class="recipe-meta">
          <text class="meta-item">🍽️ {{item.servings}}人份</text>
          <text class="meta-item">⭐ 难度{{item.difficultyLevel}}</text>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore}}" bindtap="loadMore">
      <text wx:if="{{!isLoading}}">加载更多</text>
      <text wx:else>加载中...</text>
    </view>

    <!-- 没有更多数据 -->
    <view class="no-more" wx:if="{{!hasMore && recipes.length > 0}}">
      <text>没有更多菜谱了</text>
    </view>
  </view>

  <!-- 底部提示 -->
  <view class="result-tip">
    <text class="tip-icon">💡</text>
    <text class="tip-text">识别结果不准确？点击食材名称可以手动修改</text>
  </view>
</view>