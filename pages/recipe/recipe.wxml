<!--pages/recipe/recipe.wxml-->
<scroll-view class="container" scroll-y="true" enhanced="true" show-scrollbar="false">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="nav-back" bindtap="navigateBack">
      <text class="nav-back-icon">←</text>
    </view>
    <view class="nav-title">推荐食谱</view>
    <view class="nav-help">
      <text class="help-icon">?</text>
    </view>
  </view>

  <!-- 已有食材标签 -->
  <view class="ingredients-section" wx:if="{{resultList.length > 0}}">
    <view class="ingredients-header">
      <text class="ingredients-label">已有食材：</text>
    </view>
    <view class="ingredients-tags">
      <view class="ingredient-tag" wx:for="{{resultList}}" wx:key="index">
        <text class="tag-text">{{item.name}}</text>
      </view>
      <view class="ingredient-tag add-more">
        <text class="tag-text">+1个</text>
      </view>
    </view>
  </view>

  <!-- 菜谱列表 -->
  <view class="recipe-list" wx:if="{{recipes.length > 0}}">
    <view class="recipe-card" wx:for="{{recipes}}" wx:key="id">
      <!-- 菜谱图片和基本信息 -->
      <view class="recipe-main">
        <view class="recipe-image">
          <text class="recipe-emoji">{{item.emoji || '🍽️'}}</text>
        </view>
        <view class="recipe-info">
          <view class="recipe-name">{{item.name}}</view>
          <view class="recipe-description">{{item.description}}</view>
          <view class="recipe-meta">
            <view class="meta-time">
              <text class="time-icon">⏰</text>
              <text class="time-text">{{item.cookingTime || '15'}}分钟</text>
            </view>
            <view class="meta-rating">
              <text class="rating-icon">⭐</text>
              <text class="rating-text">{{item.rating || '4.8'}}</text>
            </view>
            <view class="meta-views">
              <text class="views-icon">👥</text>
              <text class="views-text">{{item.viewCount || '1234'}}</text>
            </view>
            <view class="meta-stars">
              <text class="stars-text">☆☆☆☆☆</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 食材匹配度 -->
      <view class="ingredient-match">
        <view class="match-header">
          <text class="match-title">食材匹配度</text>
          <text class="match-ratio">{{item.matchInfo.matchedCount}}/{{item.matchInfo.totalNeeded}}</text>
        </view>
        <view class="match-progress">
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{item.matchInfo.matchPercentage}}%"></view>
          </view>
        </view>
        <view class="match-details">
          <text class="match-have">已有: {{item.matchInfo.matchedCount}}样</text>
          <text class="match-need">还需: {{item.matchInfo.needCount}}样</text>
        </view>
      </view>

      <!-- 查看详细做法按钮 -->
      <view class="recipe-action">
        <button class="detail-btn" bindtap="navigateToDetail" data-id="{{item.id}}">查看详细做法</button>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore}}" bindtap="loadMore">
    <text wx:if="{{!isLoading}}">加载更多</text>
    <text wx:else>加载中...</text>
  </view>

  <!-- 没有更多数据 -->
  <view class="no-more" wx:if="{{!hasMore && recipes.length > 0}}">
    <text>没有更多菜谱了</text>
  </view>
</scroll-view>