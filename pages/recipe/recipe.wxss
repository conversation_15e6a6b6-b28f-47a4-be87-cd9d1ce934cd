/* pages/recipe/recipe.wxss */
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--background-color);
}

/* 搜索栏样式 */
.search-bar {
  padding: 20rpx 30rpx;
  background: #FFFFFF;
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-input {
  display: flex;
  align-items: center;
  background: var(--background-color);
  border-radius: 32rpx;
  padding: 16rpx 24rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.search-placeholder {
  color: var(--text-color-secondary);
  font-size: 28rpx;
}

/* 筛选栏样式 */
.filter-bar {
  background: #FFFFFF;
  padding: 20rpx 0;
  border-bottom: 2rpx solid var(--border-color);
}

.filter-scroll {
  white-space: nowrap;
}

.filter-list {
  display: inline-flex;
  padding: 0 20rpx;
}

.filter-item {
  display: inline-block;
  padding: 12rpx 32rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
  color: var(--text-color-secondary);
  background: var(--background-color);
  border-radius: 32rpx;
  transition: all 0.3s ease;
}

.filter-item.active {
  color: #FFFFFF;
  background: var(--primary-color);
}

/* 食材标签样式 */
.ingredients-tags {
  background: #FFFFFF;
  padding: 20rpx 0;
  border-bottom: 2rpx solid var(--border-color);
}

.tags-scroll {
  white-space: nowrap;
}

.tags-list {
  display: inline-flex;
  padding: 0 20rpx;
}

.tag {
  display: inline-flex;
  align-items: center;
  padding: 8rpx 20rpx;
  margin-right: 16rpx;
  font-size: 24rpx;
  color: var(--primary-color);
  background: rgba(76, 175, 80, 0.1);
  border-radius: 24rpx;
}

.tag-close {
  margin-left: 8rpx;
  font-size: 24rpx;
  color: var(--primary-color);
  padding: 4rpx;
}

/* 菜谱列表样式 */
.recipe-list {
  flex: 1;
  padding: 20rpx;
}

.recipe-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.recipe-card {
  background: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
}

.recipe-card:active {
  transform: scale(0.98);
}

.recipe-image {
  width: 100%;
  height: 320rpx;
  background-color: #F5F5F5;
}

.recipe-info {
  padding: 20rpx;
}

.recipe-name {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 12rpx;
  display: block;
}

.recipe-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: var(--text-color-secondary);
}

.meta-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}

.recipe-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.recipe-tags .tag {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  background: var(--background-color);
  color: var(--text-color-secondary);
}

/* 加载更多样式 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx;
  color: var(--text-color-secondary);
  font-size: 24rpx;
}

.loading-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
  animation: rotate 1s linear infinite;
}

.no-more {
  text-align: center;
  padding: 30rpx;
  color: var(--text-color-secondary);
  font-size: 24rpx;
}

/* 空状态样式 */
.empty-state {
  padding: 100rpx 30rpx;
  text-align: center;
}

.empty-image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: var(--text-color-secondary);
  margin-bottom: 30rpx;
  display: block;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.result-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: env(safe-area-inset-top, 44rpx) 0 0 0;
  background: transparent;
  height: 88rpx;
  position: relative;
  z-index: 10;
}

.result-title-main-wrapper {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 88rpx;
}

.result-title-main {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
  text-align: center;
}

.retake-btn {
  color: #ff6600;
  font-size: 28rpx;
  font-weight: 500;
  padding: 0 8rpx;
  background: transparent;
  position: absolute;
  right: 32rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 11;
}

.retake-btn-center {
  width: 100%;
  margin: 32rpx 16px 0 16px;
  display: block;
  background: linear-gradient(90deg, #ff7e2d 0%, #ff3d2d 100%);
  color: #fff;
  font-size: 30rpx;
  font-weight: 500;
  border: none;
  border-radius: 16rpx;
  padding: 24rpx 0;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(255, 125, 45, 0.08);
  letter-spacing: 2rpx;
  transition: background 0.2s;
}

.retake-btn-center:active {
  background: linear-gradient(90deg, #ff3d2d 0%, #ff7e2d 100%);
  opacity: 0.85;
}

.result-card {
  background: #fff;
  border-radius: 16px;
  padding: 24px 16px;
  margin: 48rpx 16px 0 16px;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.03);
}

.result-title {
  font-weight: bold;
  font-size: 18px;
  margin-bottom: 16px;
  text-align: center;
}

.ingredient-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f6fff6;
  border: 1px solid #b6f2b6;
  border-radius: 12px;
  padding: 12px 16px;
  margin-bottom: 12px;
}

.ingredient-left {
  display: flex;
  align-items: center;
}

.check-icon {
  color: #4cd964;
  font-size: 20px;
  margin-right: 8px;
}

.ingredient-name {
  font-size: 16px;
  font-weight: 500;
}

.ingredient-rate {
  font-size: 16px;
  color: #4cd964;
  font-weight: bold;
}

.btn-recipe {
  width: 100%;
  margin-top: 32rpx;
  background: linear-gradient(90deg, #ff7e2d 0%, #ff3d2d 100%);
  color: #fff;
  font-size: 30rpx;
  font-weight: 500;
  border: none;
  border-radius: 16rpx;
  padding: 24rpx 0;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(255, 125, 45, 0.08);
  letter-spacing: 2rpx;
  transition: background 0.2s;
}

.btn-recipe:active {
  background: linear-gradient(90deg, #ff3d2d 0%, #ff7e2d 100%);
  opacity: 0.85;
}

.result-tip {
  margin: 32rpx 32rpx 0 32rpx;
  background: #f6faff;
  border-radius: 12rpx;
  padding: 20rpx 24rpx;
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #4a90e2;
}

.tip-icon {
  margin-right: 12rpx;
  font-size: 28rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #4a90e2;
}

.ingredient-edit-input {
  font-size: 16px;
  font-weight: 500;
  color: #222;
  border: 1px solid #b6f2b6;
  border-radius: 8px;
  padding: 4px 8px;
  background: #f6fff6;
  width: 120px;
}

/* 推荐菜谱列表样式 */
.recipe-list-section {
  margin: 32rpx 16px 0 16px;
  background: #fff;
  border-radius: 16px;
  padding: 24px 16px;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.03);
}

.section-title {
  font-weight: bold;
  font-size: 18px;
  margin-bottom: 16px;
  text-align: center;
  color: #222;
}

.recipe-list .recipe-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.recipe-list .recipe-card:active {
  transform: scale(0.98);
  background: #e9ecef;
}

.recipe-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.recipe-list .recipe-name {
  font-size: 16px;
  font-weight: 600;
  color: #222;
  flex: 1;
  margin-right: 12px;
}

.recipe-difficulty {
  background: linear-gradient(90deg, #ff7e2d 0%, #ff3d2d 100%);
  color: #fff;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 8px;
  white-space: nowrap;
}

.difficulty-text {
  font-size: 12px;
  font-weight: 500;
}

.recipe-description {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.recipe-list .recipe-meta {
  display: flex;
  justify-content: flex-start;
  gap: 16px;
}

.recipe-list .meta-item {
  font-size: 12px;
  color: #888;
  display: flex;
  align-items: center;
}

/* 加载更多和无更多数据样式 */
.load-more {
  text-align: center;
  padding: 20px 0;
  color: #4a90e2;
  font-size: 14px;
  cursor: pointer;
}

.load-more:active {
  opacity: 0.7;
}

.recipe-list-section .no-more {
  text-align: center;
  padding: 20px 0;
  color: #999;
  font-size: 14px;
}