// pages/profile/profile.js
const authManager = require('../../utils/auth.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    favoriteRecipes: [],
    isLoading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 检查是否已经登录
    this.checkLoginStatus();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时检查登录状态
    this.checkLoginStatus();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 检查登录状态
  checkLoginStatus() {
    console.log('检查登录状态...');
    if (authManager.checkLoginStatus()) {
      const userInfo = authManager.getCurrentUser();
      console.log('用户已登录，用户信息:', userInfo);
      this.setData({ userInfo });
      // 获取收藏列表
      this.getFavoriteRecipes();
    } else {
      console.log('用户未登录');
      this.setData({ userInfo: null, favoriteRecipes: [] });
    }
  },

  // 登录函数 - 符合官方标准流程
  async login() {
    if (this.data.isLoading) {
      return;
    }

    this.setData({ isLoading: true });
    wx.showLoading({ title: '登录中...' });

    try {
      console.log('🚀 开始标准登录流程');

      // 第一步：身份验证登录（不涉及用户信息）
      const loginResult = await authManager.wxLogin();

      if (!loginResult.success) {
        throw new Error(loginResult.message || '身份验证失败');
      }

      console.log('✅ 身份验证成功');

      // 第二步：获取用户信息（必须在用户点击事件中）
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: async (res) => {
          console.log('👤 获取用户信息成功:', res.userInfo);

          try {
            // 第三步：保存用户信息
            await authManager.saveUserInfo(res.userInfo);

            // 第四步：更新页面显示
            const userInfo = authManager.getCurrentUser();
            console.log('📱 最终用户信息:', userInfo);

            this.setData({
              userInfo: userInfo,
              isLoading: false
            });

            // 获取收藏列表
            this.getFavoriteRecipes();

            wx.hideLoading();
            wx.showToast({
              title: '登录成功',
              icon: 'success'
            });
          } catch (error) {
            console.error('❌ 保存用户信息失败:', error);
            this.setData({ isLoading: false });
            wx.hideLoading();
            wx.showToast({
              title: '保存用户信息失败',
              icon: 'none'
            });
          }
        },
        fail: (error) => {
          console.error('❌ 用户拒绝授权:', error);
          this.setData({ isLoading: false });
          wx.hideLoading();
          wx.showToast({
            title: '需要授权才能完善资料',
            icon: 'none'
          });
        }
      });

    } catch (error) {
      console.error('❌ 登录失败:', error);
      this.setData({ isLoading: false });
      wx.hideLoading();
      wx.showToast({
        title: error.message || '登录失败，请重试',
        icon: 'none'
      });
    }
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          authManager.logout();
          this.setData({
            userInfo: null,
            favoriteRecipes: []
          });
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  },

  // 获取收藏列表
  getFavoriteRecipes() {
    // TODO: 从服务器获取收藏列表
    // 这里使用模拟数据
    const mockFavorites = [
      {
        id: 1,
        name: '清炒时蔬',
        image: '🥬',
        cookTime: 15,
        difficulty: '简单',
        isFavorite: true
      },
      {
        id: 2,
        name: '红烧排骨',
        image: '🍖',
        cookTime: 45,
        difficulty: '中等',
        isFavorite: true
      }
    ]
    this.setData({ favoriteRecipes: mockFavorites })
  },

  // 切换收藏状态
  toggleFavorite(e) {
    const { id } = e.currentTarget.dataset
    const { favoriteRecipes } = this.data
    const index = favoriteRecipes.findIndex(item => item.id === id)
    
    if (index > -1) {
      const updatedRecipes = [...favoriteRecipes]
      updatedRecipes[index].isFavorite = !updatedRecipes[index].isFavorite
      this.setData({ favoriteRecipes: updatedRecipes })
      
      // TODO: 调用服务器API更新收藏状态
      wx.showToast({
        title: updatedRecipes[index].isFavorite ? '已收藏' : '已取消收藏',
        icon: 'success'
      })
    }
  }
})