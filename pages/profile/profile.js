// pages/profile/profile.js
const authManager = require('../../utils/auth.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    favoriteRecipes: [],
    isLoading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 检查是否已经登录
    this.checkLoginStatus();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时检查登录状态
    this.checkLoginStatus();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 检查登录状态
  checkLoginStatus() {
    if (authManager.checkLoginStatus()) {
      const userInfo = authManager.getCurrentUser();
      this.setData({ userInfo });
      // 获取收藏列表
      this.getFavoriteRecipes();
    } else {
      this.setData({ userInfo: null, favoriteRecipes: [] });
    }
  },

  // 登录函数
  login() {
    if (this.data.isLoading) {
      return;
    }

    // 首先获取用户信息（必须在点击事件中调用）
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: async (res) => {
        // 获取用户信息成功，开始登录流程
        this.setData({ isLoading: true });

        wx.showLoading({
          title: '登录中...'
        });

        try {
          // 调用认证管理器进行登录
          const result = await authManager.wxLogin(res.userInfo);

          if (result.success) {
            // 登录成功，更新页面数据
            const userInfo = authManager.getCurrentUser();
            this.setData({
              userInfo: userInfo,
              isLoading: false
            });

            // 获取收藏列表
            this.getFavoriteRecipes();

            wx.hideLoading();
            wx.showToast({
              title: '登录成功',
              icon: 'success'
            });
          } else {
            // 登录失败
            this.setData({ isLoading: false });
            wx.hideLoading();
            wx.showToast({
              title: result.message || '登录失败',
              icon: 'none'
            });
          }
        } catch (error) {
          console.error('登录过程出错:', error);
          this.setData({ isLoading: false });
          wx.hideLoading();
          wx.showToast({
            title: '登录失败，请重试',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        console.error('获取用户信息失败:', error);
        wx.showToast({
          title: '需要授权才能登录',
          icon: 'none'
        });
      }
    });
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          authManager.logout();
          this.setData({
            userInfo: null,
            favoriteRecipes: []
          });
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  },

  // 获取收藏列表
  getFavoriteRecipes() {
    // TODO: 从服务器获取收藏列表
    // 这里使用模拟数据
    const mockFavorites = [
      {
        id: 1,
        name: '清炒时蔬',
        image: '🥬',
        cookTime: 15,
        difficulty: '简单',
        isFavorite: true
      },
      {
        id: 2,
        name: '红烧排骨',
        image: '🍖',
        cookTime: 45,
        difficulty: '中等',
        isFavorite: true
      }
    ]
    this.setData({ favoriteRecipes: mockFavorites })
  },

  // 切换收藏状态
  toggleFavorite(e) {
    const { id } = e.currentTarget.dataset
    const { favoriteRecipes } = this.data
    const index = favoriteRecipes.findIndex(item => item.id === id)
    
    if (index > -1) {
      const updatedRecipes = [...favoriteRecipes]
      updatedRecipes[index].isFavorite = !updatedRecipes[index].isFavorite
      this.setData({ favoriteRecipes: updatedRecipes })
      
      // TODO: 调用服务器API更新收藏状态
      wx.showToast({
        title: updatedRecipes[index].isFavorite ? '已收藏' : '已取消收藏',
        icon: 'success'
      })
    }
  }
})