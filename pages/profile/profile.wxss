/* pages/profile/profile.wxss */
.profile-header-spacer {
  height: 120rpx; /* Adjust this value as needed */
}

.container {
  min-height: 100vh;
  background-color: #FFF6F0;
  padding: 40rpx 32rpx;
  box-sizing: border-box;
  padding-bottom: 50rpx;
}
.profile-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  margin-bottom: 20rpx;
}

.avatar-placeholder {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  margin-bottom: 20rpx;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-icon {
  font-size: 80rpx;
  color: #ccc;
}

.nickname {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.nickname-placeholder {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.btn-login {
  margin-top: 20rpx;
  padding: 12rpx 40rpx;
  background: linear-gradient(90deg, #ff7e2d 0%, #ff3d2d 100%);
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(255, 61, 45, 0.3);
}

.btn-login[disabled] {
  background: #ccc;
  box-shadow: none;
}

.btn-logout {
  margin-top: 20rpx;
  padding: 12rpx 40rpx;
  background: #f5f5f5;
  color: #666;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: 1rpx solid #ddd;
}
.section { margin: 30rpx 0; }
.section-header { margin-bottom: 20rpx; }
.section-title { font-size: 32rpx; font-weight: bold; color: #333; }
.favorite-list { width: 100%; }
.favorite-item { display: flex; align-items: center; padding: 20rpx; background: #fff; border-radius: 8rpx; box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1); margin-bottom: 20rpx; }
.favorite-image { 
  width: 100rpx; 
  height: 100rpx; 
  border-radius: 8rpx; 
  margin-right: 20rpx; 
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  background-color: #f5f5f5;
}
.favorite-info { flex: 1; }
.favorite-name { font-size: 28rpx; color: #333; margin-bottom: 6rpx; display: block; }
.favorite-meta { display: flex; gap: 20rpx; font-size: 24rpx; color: #666; }
.favorite-btn { padding: 10rpx; color: #ff6b6b; font-size: 28rpx; }
.empty-state { text-align: center; padding: 40rpx; color: #999; font-size: 28rpx; }