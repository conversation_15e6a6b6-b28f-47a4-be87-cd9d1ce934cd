<!--pages/profile/profile.wxml-->
<navigation-bar title="我的" back="{{false}}" color="black" background="#FFF6F0"></navigation-bar>
<view class="container">
  <view class="profile-header-spacer"></view>
  <view class="profile-header">
    <image wx:if="{{userInfo}}" class="avatar" src="{{userInfo.avatarUrl}}" />
    <view wx:else class="avatar-placeholder">
      <text class="avatar-icon">👤</text>
    </view>
    <text wx:if="{{userInfo}}" class="nickname">{{userInfo.nickName}}</text>
    <text wx:else class="nickname-placeholder">未登录</text>

    <button wx:if="{{!userInfo}}"
            bindtap="login"
            class="btn-login"
            loading="{{isLoading}}"
            disabled="{{isLoading}}">
      {{isLoading ? '登录中...' : '微信登录'}}
    </button>

    <button wx:if="{{userInfo}}"
            bindtap="logout"
            class="btn-logout">
      退出登录
    </button>
  </view>
  <view class="section" wx:if="{{userInfo}}">
    <view class="section-header">
      <text class="section-title">收藏夹</text>
    </view>
    <view class="favorite-list">
      <block wx:if="{{favoriteRecipes.length > 0}}">
        <view class="favorite-item" wx:for="{{favoriteRecipes}}" wx:key="id">
          <text class="favorite-image">{{item.image}}</text>
          <view class="favorite-info">
            <text class="favorite-name">{{item.name}}</text>
            <view class="favorite-meta">
              <text class="favorite-time">{{item.cookTime}}分钟</text>
              <text class="favorite-difficulty">{{item.difficulty}}</text>
            </view>
          </view>
          <view class="favorite-btn" bindtap="toggleFavorite" data-id="{{item.id}}">
            <text>{{item.isFavorite ? '♥' : '♡'}}</text>
          </view>
        </view>
      </block>
      <view wx:else class="empty-state">
        <text class="empty-text">暂无收藏菜品</text>
      </view>
    </view>
  </view>
</view>