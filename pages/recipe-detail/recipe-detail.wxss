/* pages/recipe-detail/recipe-detail.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 加载状态样式 */
.loading-container, .error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading-content, .error-content {
  text-align: center;
}

.loading-text {
  font-size: 32rpx;
  color: #666;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  display: block;
}

.error-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 30rpx;
  display: block;
}

.retry-btn {
  background: linear-gradient(90deg, #ff7e2d 0%, #ff3d2d 100%);
  color: #fff;
  border: none;
  border-radius: 16rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: env(safe-area-inset-top, 44rpx) 32rpx 20rpx 32rpx;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.nav-back {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f5f5f5;
  border-radius: 30rpx;
}

.nav-back-icon {
  font-size: 32rpx;
  color: #333;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin: 0 20rpx;
}

.nav-favorite {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f5f5f5;
  border-radius: 30rpx;
}

.favorite-icon {
  font-size: 32rpx;
  color: #ccc;
  transition: color 0.3s ease;
}

.favorite-icon.active {
  color: #ff3d2d;
}

/* 菜谱信息样式 */
.recipe-info {
  background: #fff;
  padding: 32rpx;
  margin: 20rpx 16rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.03);
}

.recipe-header {
  margin-bottom: 20rpx;
}

.recipe-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #222;
  margin-bottom: 16rpx;
  display: block;
}

.recipe-meta {
  display: flex;
  gap: 30rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
}

.meta-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
}

.recipe-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-top: 16rpx;
}

/* 区块通用样式 */
.section {
  background: #fff;
  padding: 32rpx;
  margin: 20rpx 16rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.03);
}

.section-header {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #222;
}

/* 食材清单样式 */
.ingredients-container {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.ingredient-group {
  border-radius: 12rpx;
  overflow: hidden;
}

.group-title {
  background: #f8f9fa;
  padding: 16rpx 20rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  border-bottom: 1rpx solid #e9ecef;
}

.ingredients-list {
  background: #fff;
}

.ingredient-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.ingredient-item:last-child {
  border-bottom: none;
}

.ingredient-name {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.ingredient-quantity {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

/* 制作步骤样式 */
.steps-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.step-item {
  display: flex;
  gap: 24rpx;
  align-items: flex-start;
}

.step-number {
  width: 56rpx;
  height: 56rpx;
  background: linear-gradient(90deg, #ff7e2d 0%, #ff3d2d 100%);
  border-radius: 28rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 28rpx;
  font-weight: 600;
  flex-shrink: 0;
  margin-top: 4rpx;
}

.step-content {
  flex: 1;
}

.step-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 12rpx;
}

.step-duration {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: #f0f8ff;
  border-radius: 20rpx;
  border: 1rpx solid #e6f3ff;
  width: fit-content;
}

.duration-icon {
  font-size: 24rpx;
}

.duration-text {
  font-size: 24rpx;
  color: #4a90e2;
  font-weight: 500;
}

