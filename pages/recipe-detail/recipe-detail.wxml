<!--pages/recipe-detail/recipe-detail.wxml-->
<view class="container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-content">
      <text class="loading-text">加载中...</text>
    </view>
  </view>

  <!-- 错误状态 -->
  <view class="error-container" wx:elif="{{loadError}}">
    <view class="error-content">
      <text class="error-icon">😞</text>
      <text class="error-text">加载失败</text>
      <button class="retry-btn" bindtap="retryLoad">重试</button>
    </view>
  </view>

  <!-- 菜谱详情内容 -->
  <view class="recipe-content" wx:elif="{{recipeDetail}}">
    <!-- 顶部导航栏 -->
    <view class="custom-navbar">
      <view class="nav-back" bindtap="navigateBack">
        <text class="nav-back-icon">←</text>
      </view>
      <view class="nav-title">{{recipeDetail.name}}</view>
      <view class="nav-favorite" bindtap="toggleFavorite">
        <text class="favorite-icon {{isFavorite ? 'active' : ''}}">{{isFavorite ? '♥' : '♡'}}</text>
      </view>
    </view>

    <!-- 菜谱基本信息 -->
    <view class="recipe-info">
      <view class="recipe-header">
        <text class="recipe-name">{{recipeDetail.name}}</text>
        <view class="recipe-meta">
          <view class="meta-item">
            <text class="meta-icon">🧩</text>
            <text>{{recipeDetail.difficultyLevelDesc}}</text>
          </view>
          <view class="meta-item">
            <text class="meta-icon">🍽️</text>
            <text>{{recipeDetail.servings}}人份</text>
          </view>
        </view>
      </view>

      <!-- 简介 -->
      <view class="recipe-desc" wx:if="{{recipeDetail.description}}">
        <text>{{recipeDetail.description}}</text>
      </view>
    </view>

    <!-- 食材清单 -->
    <view class="section">
      <view class="section-header">
        <text class="section-title">食材清单</text>
      </view>
      <view class="ingredients-container">
        <view class="ingredient-group" wx:for="{{ingredients}}" wx:key="type">
          <view class="group-title">{{item.typeDesc}}</view>
          <view class="ingredients-list">
            <view class="ingredient-item" wx:for="{{item.items}}" wx:key="ingredientId" wx:for-item="ingredient">
              <text class="ingredient-name">{{ingredient.ingredientName}}</text>
              <text class="ingredient-quantity">{{ingredient.quantity}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 制作步骤 -->
    <view class="section">
      <view class="section-header">
        <text class="section-title">制作步骤</text>
      </view>
      <view class="steps-list">
        <view class="step-item" wx:for="{{steps}}" wx:key="id">
          <view class="step-number">{{item.stepOrder}}</view>
          <view class="step-content">
            <text class="step-text">{{item.description}}</text>
            <view class="step-duration" wx:if="{{item.durationDesc}}">
              <text class="duration-icon">⏱️</text>
              <text class="duration-text">{{item.durationDesc}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
