// pages/recipe-detail/recipe-detail.js
const apiConfig = require('../../config/api.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    recipeId: null,
    recipeDetail: null,
    isLoading: true,
    loadError: false,
    ingredients: [],
    steps: [],
    isFavorite: false,
    // 新增数据字段
    ingredientsList: [],  // 扁平化的食材列表，用于显示状态
    stepsList: [],        // 处理后的步骤列表
    nutritionInfo: {},    // 营养信息
    userIngredients: []   // 用户拥有的食材列表
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { id } = options;
    if (id) {
      this.setData({ recipeId: id });
      this.loadRecipeDetail(id);
    } else {
      wx.showToast({
        title: '菜谱ID不能为空',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 加载菜谱详情
  loadRecipeDetail(recipeId) {
    this.setData({ isLoading: true, loadError: false });

    wx.showLoading({
      title: '加载中...'
    });

    const url = `${apiConfig.baseUrl}${apiConfig.api.recipeDetail}/${recipeId}`;
    console.log('请求菜谱详情URL:', url);

    wx.request({
      url: url,
      method: 'GET',
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        wx.hideLoading();
        console.log('菜谱详情响应:', res);

        if (res.statusCode === 200 && res.data && res.data.code === 0) {
          const recipeData = res.data.data;

          // 处理食材数据，按类型分组
          const ingredientGroups = this.groupIngredientsByType(recipeData.ingredients || []);

          // 处理扁平化食材列表，用于显示状态
          const ingredientsList = this.processIngredientsList(recipeData.ingredients || []);

          // 处理制作步骤
          const stepsList = this.processStepsList(recipeData.steps || []);

          // 处理营养信息
          const nutritionInfo = this.processNutritionInfo(recipeData);

          this.setData({
            recipeDetail: recipeData,
            ingredients: ingredientGroups,
            steps: recipeData.steps || [],
            ingredientsList: ingredientsList,
            stepsList: stepsList,
            nutritionInfo: nutritionInfo,
            isLoading: false,
            loadError: false
          });

          // 设置页面标题
          wx.setNavigationBarTitle({
            title: recipeData.name || '菜谱详情'
          });

          // 检查收藏状态
          this.checkFavoriteStatus(recipeId);
        } else {
          console.error('获取菜谱详情失败，状态码:', res.statusCode);
          console.error('响应数据:', res.data);

          this.setData({
            isLoading: false,
            loadError: true
          });

          wx.showToast({
            title: res.data && res.data.msg ? res.data.msg : '获取菜谱详情失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        wx.hideLoading();
        console.error('获取菜谱详情失败:', error);

        this.setData({
          isLoading: false,
          loadError: true
        });

        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 按类型分组食材
  groupIngredientsByType(ingredients) {
    const groups = {};

    ingredients.forEach(ingredient => {
      const type = ingredient.type || 'other';
      const typeDesc = ingredient.typeDesc || '其他';

      if (!groups[type]) {
        groups[type] = {
          type: type,
          typeDesc: typeDesc,
          items: []
        };
      }

      groups[type].items.push(ingredient);
    });

    // 转换为数组并排序（主料在前，调料在后）
    const sortOrder = { 'main': 1, 'seasoning': 2, 'other': 3 };
    return Object.values(groups).sort((a, b) => {
      return (sortOrder[a.type] || 999) - (sortOrder[b.type] || 999);
    });
  },

  // 处理扁平化食材列表，用于显示状态
  processIngredientsList(ingredients) {
    // 如果没有食材数据，使用示例数据
    if (!ingredients || ingredients.length === 0) {
      return this.getSampleIngredients();
    }

    // 获取用户拥有的食材列表（从本地存储或全局状态获取）
    const userIngredients = this.getUserIngredients();

    return ingredients.map(ingredient => {
      // 检查用户是否拥有该食材
      const hasIngredient = userIngredients.includes(ingredient.ingredientName);

      return {
        ...ingredient,
        hasIngredient: hasIngredient
      };
    });
  },

  // 获取示例食材数据
  getSampleIngredients() {
    const userIngredients = this.getUserIngredients();

    const sampleIngredients = [
      { id: 1, ingredientName: '西红柿', quantity: '适量' },
      { id: 2, ingredientName: '鸡蛋', quantity: '适量' },
      { id: 3, ingredientName: '面条', quantity: '适量' },
      { id: 4, ingredientName: '大葱', quantity: '适量' },
      { id: 5, ingredientName: '盐', quantity: '适量' },
      { id: 6, ingredientName: '糖', quantity: '适量' }
    ];

    return sampleIngredients.map(ingredient => {
      const hasIngredient = userIngredients.includes(ingredient.ingredientName);
      return {
        ...ingredient,
        hasIngredient: hasIngredient
      };
    });
  },

  // 处理制作步骤列表
  processStepsList(steps) {
    // 如果没有步骤数据，使用示例数据
    if (!steps || steps.length === 0) {
      return this.getSampleSteps();
    }

    return steps.map(step => {
      return {
        ...step,
        expanded: false // 用于控制步骤展开状态
      };
    });
  },

  // 获取示例步骤数据
  getSampleSteps() {
    return [
      {
        id: 1,
        stepOrder: 1,
        description: '准备食材：西红柿切块，鸡蛋打散，大葱切段',
        expanded: true
      },
      {
        id: 2,
        stepOrder: 2,
        description: '热锅下油，倒入蛋液炒熟盛起备用',
        expanded: true
      },
      {
        id: 3,
        stepOrder: 3,
        description: '锅内留底油，下西红柿块炒出汁水',
        expanded: true
      },
      {
        id: 4,
        stepOrder: 4,
        description: '加入适量盐和糖调味',
        expanded: true
      },
      {
        id: 5,
        stepOrder: 5,
        description: '倒入炒蛋翻炒均匀',
        expanded: true
      },
      {
        id: 6,
        stepOrder: 6,
        description: '煮面条至8分熟，倒入锅中',
        expanded: true
      },
      {
        id: 7,
        stepOrder: 7,
        description: '加入适量面汤，焖煮2分钟',
        expanded: true
      },
      {
        id: 8,
        stepOrder: 8,
        description: '撒上葱花即可出锅',
        expanded: true
      }
    ];
  },

  // 处理营养信息
  processNutritionInfo(recipeData) {
    // 如果API返回了营养信息，使用API数据，否则使用默认值
    return {
      calories: recipeData.calories || '320',
      protein: recipeData.protein || '18g',
      carbs: recipeData.carbs || '45g',
      fat: recipeData.fat || '8g'
    };
  },

  // 获取用户拥有的食材列表
  getUserIngredients() {
    // 从本地存储获取用户识别的食材
    const recognizedIngredients = wx.getStorageSync('recognizedIngredients') || [];
    return recognizedIngredients.map(item => item.name || item.ingredientName).filter(Boolean);
  },

  // 检查收藏状态
  checkFavoriteStatus(recipeId) {
    // 从本地存储获取收藏列表
    const favorites = wx.getStorageSync('favoriteRecipes') || [];
    const isFavorite = favorites.some(item => item.id == recipeId);
    this.setData({ isFavorite });
  },

  // 切换收藏状态
  toggleFavorite() {
    const { recipeDetail, isFavorite } = this.data;
    if (!recipeDetail) return;

    const favorites = wx.getStorageSync('favoriteRecipes') || [];

    if (isFavorite) {
      // 取消收藏
      const newFavorites = favorites.filter(item => item.id != recipeDetail.id);
      wx.setStorageSync('favoriteRecipes', newFavorites);
      this.setData({ isFavorite: false });

      wx.showToast({
        title: '已取消收藏',
        icon: 'success'
      });
    } else {
      // 添加收藏
      const favoriteItem = {
        id: recipeDetail.id,
        name: recipeDetail.name,
        description: recipeDetail.description,
        difficultyLevelDesc: recipeDetail.difficultyLevelDesc,
        servings: recipeDetail.servings,
        addTime: new Date().getTime()
      };

      favorites.push(favoriteItem);
      wx.setStorageSync('favoriteRecipes', favorites);
      this.setData({ isFavorite: true });

      wx.showToast({
        title: '已添加收藏',
        icon: 'success'
      });
    }
  },

  // 重新加载
  retryLoad() {
    if (this.data.recipeId) {
      this.loadRecipeDetail(this.data.recipeId);
    }
  },

  navigateBack() {
    wx.navigateBack();
  },

  // 显示帮助信息
  showHelp() {
    wx.showModal({
      title: '食材状态说明',
      content: '✓ 表示您已拥有该食材\n✗ 表示您还需要准备该食材\n\n食材状态基于您之前的拍照识别结果',
      showCancel: false,
      confirmText: '知道了'
    });
  }
})