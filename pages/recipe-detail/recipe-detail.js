// pages/recipe-detail/recipe-detail.js
const apiConfig = require('../../config/api.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    recipeId: null,
    recipeDetail: null,
    isLoading: true,
    loadError: false,
    ingredients: [],
    steps: [],
    isFavorite: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { id } = options;
    if (id) {
      this.setData({ recipeId: id });
      this.loadRecipeDetail(id);
    } else {
      wx.showToast({
        title: '菜谱ID不能为空',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 加载菜谱详情
  loadRecipeDetail(recipeId) {
    this.setData({ isLoading: true, loadError: false });

    wx.showLoading({
      title: '加载中...'
    });

    const url = `${apiConfig.baseUrl}${apiConfig.api.recipeDetail}/${recipeId}`;
    console.log('请求菜谱详情URL:', url);

    wx.request({
      url: url,
      method: 'GET',
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        wx.hideLoading();
        console.log('菜谱详情响应:', res);

        if (res.statusCode === 200 && res.data && res.data.code === 200) {
          const recipeData = res.data.data;

          // 处理食材数据，按类型分组
          const ingredientGroups = this.groupIngredientsByType(recipeData.ingredients || []);

          this.setData({
            recipeDetail: recipeData,
            ingredients: ingredientGroups,
            steps: recipeData.steps || [],
            isLoading: false,
            loadError: false
          });

          // 设置页面标题
          wx.setNavigationBarTitle({
            title: recipeData.name || '菜谱详情'
          });

          // 检查收藏状态
          this.checkFavoriteStatus(recipeId);
        } else {
          console.error('获取菜谱详情失败，状态码:', res.statusCode);
          console.error('响应数据:', res.data);

          this.setData({
            isLoading: false,
            loadError: true
          });

          wx.showToast({
            title: res.data && res.data.msg ? res.data.msg : '获取菜谱详情失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        wx.hideLoading();
        console.error('获取菜谱详情失败:', error);

        this.setData({
          isLoading: false,
          loadError: true
        });

        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 按类型分组食材
  groupIngredientsByType(ingredients) {
    const groups = {};

    ingredients.forEach(ingredient => {
      const type = ingredient.type || 'other';
      const typeDesc = ingredient.typeDesc || '其他';

      if (!groups[type]) {
        groups[type] = {
          type: type,
          typeDesc: typeDesc,
          items: []
        };
      }

      groups[type].items.push(ingredient);
    });

    // 转换为数组并排序（主料在前，调料在后）
    const sortOrder = { 'main': 1, 'seasoning': 2, 'other': 3 };
    return Object.values(groups).sort((a, b) => {
      return (sortOrder[a.type] || 999) - (sortOrder[b.type] || 999);
    });
  },

  // 检查收藏状态
  checkFavoriteStatus(recipeId) {
    // 从本地存储获取收藏列表
    const favorites = wx.getStorageSync('favoriteRecipes') || [];
    const isFavorite = favorites.some(item => item.id == recipeId);
    this.setData({ isFavorite });
  },

  // 切换收藏状态
  toggleFavorite() {
    const { recipeDetail, isFavorite } = this.data;
    if (!recipeDetail) return;

    const favorites = wx.getStorageSync('favoriteRecipes') || [];

    if (isFavorite) {
      // 取消收藏
      const newFavorites = favorites.filter(item => item.id != recipeDetail.id);
      wx.setStorageSync('favoriteRecipes', newFavorites);
      this.setData({ isFavorite: false });

      wx.showToast({
        title: '已取消收藏',
        icon: 'success'
      });
    } else {
      // 添加收藏
      const favoriteItem = {
        id: recipeDetail.id,
        name: recipeDetail.name,
        description: recipeDetail.description,
        difficultyLevelDesc: recipeDetail.difficultyLevelDesc,
        servings: recipeDetail.servings,
        addTime: new Date().getTime()
      };

      favorites.push(favoriteItem);
      wx.setStorageSync('favoriteRecipes', favorites);
      this.setData({ isFavorite: true });

      wx.showToast({
        title: '已添加收藏',
        icon: 'success'
      });
    }
  },

  // 重新加载
  retryLoad() {
    if (this.data.recipeId) {
      this.loadRecipeDetail(this.data.recipeId);
    }
  },

  navigateBack() {
    wx.navigateBack();
  }
})