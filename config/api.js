// API配置
const config = {
  // 开发环境
  development: {
    baseUrl: 'http://106.55.155.115:8080',
    api: {
      // 食材识别接口 - 支持Base64格式的多文件上传
      // 请求格式: POST /cook/upload/photos
      // 参数: { files: [{ fileName, fileId, fileSize, fileData }] }
      // fileData格式: "data:image/jpeg;base64,/9j/4AAQ..."
      uploadPhotos: '/cook/upload/photos',
      // 在这里添加其他接口路径
      // example: '/cook/other/api'
    }
  },
  // 生产环境
  production: {
    baseUrl: 'https://api.yourdomain.com', // 替换为实际的生产环境域名
    api: {
      // 食材识别接口 - 支持Base64格式的多文件上传
      // 请求格式: POST /cook/upload/photos
      // 参数: { files: [{ fileName, fileId, fileSize, fileData }] }
      // fileData格式: "data:image/jpeg;base64,/9j/4AAQ..."
      uploadPhotos: '/cook/upload/photos',
      // 在这里添加其他接口路径
      // example: '/cook/other/api'
    }
  }
};

// 获取当前环境
// 微信小程序中，可以通过 __wxConfig 判断环境
const env = __wxConfig.envVersion === 'release' ? 'production' : 'development';

// 导出当前环境的配置
module.exports = {
  baseUrl: config[env].baseUrl,
  api: config[env].api,
  // 导出当前环境，方便其他地方使用
  env: env
}; 